# PDF图层保留功能使用说明

## 🎯 功能概述

本项目现已支持保存**保留原始图层的PDF格式**图表，便于在Adobe Illustrator、CorelDRAW等矢量图形编辑软件中进行后期编辑和调整。

## ✨ 主要特性

### 🔧 技术特点
- **保留图层结构**: PDF中的每个图形元素都可以独立选择和编辑
- **矢量格式**: 无损缩放，适合高质量印刷和展示
- **完整元数据**: 包含创建者、主题、关键词等信息
- **高分辨率**: 300 DPI输出，确保图像质量
- **标准兼容**: 符合PDF/A标准，长期保存无忧

### 📊 支持的图表类型
1. **结果可视化界面**
   - ROC曲线
   - 混淆矩阵
   - 特征重要性
   - 学习曲线
   - 性能比较雷达图

2. **SHAP分析界面**
   - SHAP摘要图
   - SHAP决策图
   - SHAP依赖图
   - SHAP力图
   - SHAP瀑布图

## 🚀 使用方法

### 方法1: 结果可视化界面
1. 在"结果可视化"选项卡中选择已训练的模型
2. 选择要查看的图表类型
3. 点击"📈 单模型可视化"生成图表
4. 点击"💾 保存图表"按钮
5. 在弹出的对话框中选择保存模式：
   - **仅保存当前图表**: 保存当前显示的单个图表
   - **保存当前模型所有图表到一个PDF**: 合并所有图表到一个PDF文件
   - **保存当前模型所有图表到不同PDF**: 每个图表类型单独保存
6. 选择"PDF格式"选项
7. 选择保存位置和文件名

### 方法2: SHAP分析界面
1. 在"结果可视化"界面点击"SHAP分析"按钮
2. 在SHAP分析控制界面中浏览不同类型的图表
3. 使用以下保存选项：
   - **💾 保存当前图**: 保存当前显示的SHAP图表
   - **📁 保存所有图**: 批量保存所有SHAP图表
4. 在格式选择对话框中选择"PDF格式"
5. 选择保存位置

## 🎨 PDF参数配置

### 保留图层的关键参数
```python
pdf_params = {
    'format': 'pdf',           # PDF格式
    'dpi': 300,               # 高分辨率
    'bbox_inches': 'tight',   # 紧凑边界
    'facecolor': 'white',     # 白色背景
    'edgecolor': 'none',      # 无边框
    'transparent': False,     # 非透明背景
    'pad_inches': 0.1,        # 边距
    'metadata': {             # 元数据信息
        'Creator': 'Multi-Model Analysis Tool',
        'Subject': '图表类型',
        'Title': '图表标题',
        'Keywords': '关键词'
    }
}
```

### 与标准PDF的区别
| 特性 | 标准PDF | 保留图层PDF |
|------|---------|-------------|
| 图层分离 | ❌ | ✅ |
| 元素可编辑 | ❌ | ✅ |
| 文字可选择 | ✅ | ✅ |
| 矢量图形 | ✅ | ✅ |
| 文件大小 | 较小 | 略大 |
| 编辑友好性 | 一般 | 优秀 |

## 🛠️ 在图形编辑软件中使用

### Adobe Illustrator
1. 打开保存的PDF文件
2. 选择"链接"面板查看图层结构
3. 使用"直接选择工具"(A)选择单个元素
4. 可以独立编辑：
   - 线条颜色和粗细
   - 文字内容和字体
   - 填充颜色和透明度
   - 图形形状和位置

### CorelDRAW
1. 导入PDF文件
2. 选择"保持图层"选项
3. 在"对象管理器"中查看图层结构
4. 双击元素进行编辑

### Inkscape (免费替代)
1. 打开PDF文件
2. 选择"导入"选项
3. 在"图层"面板中管理图层
4. 使用选择工具编辑单个元素

## 🧪 测试功能

项目包含测试脚本 `test_pdf_layers.py`，可以验证PDF图层保留功能：

```bash
python test_pdf_layers.py
```

测试脚本会生成：
- 标准PDF文件（对比用）
- 保留图层PDF文件
- PNG格式文件（对比用）
- SHAP样式测试图表

## 💡 使用建议

### 最佳实践
1. **选择合适的图表**: 复杂图表（如SHAP摘要图）最能体现图层保留的优势
2. **检查兼容性**: 确保目标编辑软件支持PDF图层
3. **备份原文件**: 编辑前建议备份原始PDF文件
4. **测试输出**: 首次使用时建议先测试小图表

### 常见问题
**Q: 为什么PDF文件比PNG大？**
A: PDF保留了矢量信息和图层结构，文件稍大但质量更高。

**Q: 所有PDF阅读器都能正确显示吗？**
A: 是的，保留图层的PDF完全兼容标准PDF阅读器。

**Q: 可以转换为其他矢量格式吗？**
A: 可以在图形编辑软件中导出为SVG、EPS等格式。

## 🔄 版本更新

### v1.0 (当前版本)
- ✅ 支持所有主要图表类型的图层保留
- ✅ 完整的元数据支持
- ✅ GUI界面集成
- ✅ 批量保存功能

### 计划功能
- 🔄 自定义图层命名
- 🔄 图层分组功能
- 🔄 更多元数据选项

---

**注意**: 此功能专为需要在专业图形软件中进一步编辑图表的用户设计。如果只需要查看和打印，标准PDF格式已足够使用。
