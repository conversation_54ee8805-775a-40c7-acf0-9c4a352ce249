# SHAP GUI 可视化功能使用说明

## 🎯 功能概述

新增的SHAP GUI可视化功能为单模型训练结果提供了完整的SHAP可解释性分析界面，支持多种图表类型的切换和翻页浏览。

## 🚀 如何使用

### 1. 启动SHAP分析

1. **训练模型**: 首先在"模型训练"页面完成模型训练
2. **进入可视化**: 切换到"结果可视化"页面
3. **选择模型**: 在"选择模型"下拉框中选择已训练的模型
4. **选择图表类型**: 在"图表类型"下拉框中选择"SHAP分析"
5. **点击可视化**: 点击"📈 单模型可视化"按钮

### 2. SHAP分析窗口

点击可视化后，会弹出一个专门的SHAP分析窗口，包含以下功能：

#### 📊 支持的图表类型

| 图表类型 | 数量 | 说明 |
|---------|------|------|
| **摘要图** | 1张 | 显示所有特征的SHAP值分布 |
| **依赖图** | 5张 | 显示前5个重要特征的依赖关系 |
| **力图** | 3张 | 显示前3个样本的特征贡献 |
| **决策图** | 1张 | 显示前50个样本的决策路径 |
| **瀑布图** | 3张 | 显示样本0、5、10的特征贡献瀑布 |

#### 🎮 界面控制

**图表类型选择**:
- 使用下拉框切换不同的SHAP图表类型
- 自动显示当前类型的第一张图

**翻页控制** (仅多图类型显示):
- **◀ 上一页**: 浏览上一张图
- **下一页 ▶**: 浏览下一张图
- **页码显示**: 显示当前页/总页数

**操作按钮**:
- **🔄 刷新**: 重新生成当前图表
- **💾 保存当前图**: 保存当前显示的图表
- **📁 保存所有图**: 批量保存所有SHAP图表

#### 📈 图表详情

**1. SHAP摘要图**
- 显示所有特征的SHAP值分布
- 颜色表示特征值大小
- Y轴按特征重要性排序

**2. SHAP依赖图**
- 显示特征值与SHAP值的关系
- 支持翻页浏览前5个重要特征
- X轴为特征值，Y轴为SHAP值

**3. SHAP力图**
- 显示单个样本的特征贡献
- 红色条表示正贡献，蓝色条表示负贡献
- 显示基准值和预测值

**4. SHAP决策图**
- 显示前50个样本的决策路径
- 从基准值到最终预测的路径

**5. SHAP瀑布图**
- 显示单个样本的特征贡献累积
- 从基准值逐步累积到最终预测值

## 💾 保存功能

### 保存当前图表
- **格式选择对话框**: 点击"💾 保存当前图"后弹出格式选择窗口
- **PNG格式**: 推荐用于网页展示和文档插入
- **PDF格式**: 推荐用于打印和学术报告
- **高分辨率输出**: 300 DPI确保图像质量
- **自动命名**: 根据模型名称和图表类型自动生成文件名

### 批量保存所有图表
提供三种批量保存选项：

#### 1. 分别保存为PNG文件
- 每个图表保存为独立的PNG文件
- 适合网页展示和演示文稿
- 文件体积较小，加载速度快

#### 2. 分别保存为PDF文件
- 每个图表保存为独立的PDF文件
- 适合打印和学术论文
- 矢量格式，无损缩放

#### 3. 保存到单个PDF报告
- 所有图表合并到一个PDF文件
- **自动生成封面页**: 包含模型信息、数据统计、生成时间
- **完整的分析报告**: 按图表类型有序排列
- **适合正式报告**: 学术论文、业务汇报、技术文档

## 🔧 技术特性

### 自动适配
- **模型类型**: 自动检测并使用合适的SHAP解释器
- **数据格式**: 支持DataFrame和numpy数组
- **SHAP值格式**: 自动处理二分类和多分类情况

### 性能优化
- **智能采样**: 大数据集自动采样以提高性能
- **缓存机制**: SHAP值计算结果缓存，避免重复计算
- **异步加载**: 界面响应不阻塞

### 错误处理
- **库检查**: 自动检测SHAP库是否安装
- **模型兼容性**: 检查模型是否支持SHAP分析
- **数据验证**: 验证数据格式和完整性

## 🎨 界面特色

### 用户友好
- **直观操作**: 简单的下拉框和按钮操作
- **实时反馈**: 状态栏显示当前操作进度
- **错误提示**: 清晰的错误信息和解决建议

### 专业展示
- **高质量图表**: 使用matplotlib生成专业图表
- **一致性设计**: 与命令行版本保持一致的图表样式
- **可定制性**: 支持不同的保存格式和分辨率

## 🔍 使用示例

### 典型工作流程

1. **数据准备**: 加载数据文件
2. **模型训练**: 选择RandomForest等模型进行训练
3. **启动SHAP**: 选择"SHAP分析"并点击可视化
4. **探索分析**: 
   - 查看摘要图了解整体特征重要性
   - 浏览依赖图理解特征与预测的关系
   - 检查力图和瀑布图分析具体样本
5. **保存结果**: 保存感兴趣的图表或批量保存所有图表

### 最佳实践

- **先看摘要图**: 了解整体特征重要性分布
- **重点关注依赖图**: 理解重要特征的影响模式
- **分析异常样本**: 使用力图和瀑布图分析特殊样本
- **选择合适的保存格式**:
  - 演示用途 → PNG格式
  - 学术报告 → PDF格式
  - 完整分析 → 单个PDF报告

### 📄 PDF报告功能详解

#### PDF报告结构
1. **封面页**:
   - 报告标题和模型名称
   - 数据集基本信息（样本数、特征数）
   - 生成时间和图表统计

2. **图表页面**:
   - SHAP摘要图（整体特征重要性）
   - SHAP决策图（样本决策路径）
   - SHAP依赖图（重要特征依赖关系，多页）
   - SHAP力图（样本特征贡献，多页）
   - SHAP瀑布图（样本贡献累积，多页）

#### PDF报告优势
- **专业格式**: 适合学术论文和商业报告
- **完整性**: 包含所有SHAP分析结果
- **便于分享**: 单个文件包含完整分析
- **打印友好**: 高质量矢量图形
- **自动排版**: 无需手动整理图表

## 🚨 注意事项

1. **SHAP库要求**: 需要安装shap库 (`pip install shap`)
2. **PIL库要求**: 需要安装Pillow库 (`pip install Pillow`) 用于图像处理
3. **模型兼容性**: 主要支持树模型(RandomForest, XGBoost等)
4. **数据大小**: 大数据集可能需要较长计算时间
5. **内存使用**: SHAP计算可能消耗较多内存
6. **中文字体**: 界面中文可能显示为方框，但不影响功能使用

## 🔧 已修复的问题

- ✅ **图表显示问题**: 修复了"The passed figure is not managed by pyplot"错误
- ✅ **文件保存问题**: 修复了保存对话框参数错误
- ✅ **图表兼容性**: 使用临时文件方式解决SHAP图表与matplotlib的冲突
- ✅ **批量保存问题**: 修复了依赖图在批量保存时显示空白的问题
- ✅ **PDF保存支持**: 新增PDF格式保存选项和单个PDF报告功能
- ✅ **稳定性提升**: 增强了错误处理和异常恢复机制
- ✅ **一致性保证**: 确保显示和保存使用相同的图表生成方法

## 🆕 新增功能

### PDF保存功能
- **单图PDF保存**: 支持将当前图表保存为PDF格式
- **批量PDF保存**: 支持将所有图表分别保存为PDF文件
- **PDF分析报告**: 支持将所有图表合并到单个PDF报告
- **专业排版**: 自动生成封面页和规范的报告格式
- **高质量输出**: 300 DPI矢量图形，适合打印和学术使用

## 🔄 与命令行版本的关系

- **功能一致**: GUI版本与命令行版本生成相同的图表
- **增强体验**: 提供更好的交互体验和翻页功能
- **兼容性**: 可以与现有的命令行工作流程配合使用

---

**🎉 现在您可以在GUI中享受完整的SHAP可解释性分析体验！**
