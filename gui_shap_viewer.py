#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI SHAP可视化查看器
支持多种SHAP图表类型的切换和翻页功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np
from pathlib import Path
import sys

# 添加代码路径
sys.path.append(str(Path(__file__).parent / 'code'))

class SHAPViewer:
    """SHAP可视化查看器"""
    
    def __init__(self, parent_gui):
        """初始化SHAP查看器"""
        self.parent_gui = parent_gui
        self.current_shap_type = "摘要图"
        self.current_page = 0
        self.shap_data = {}
        self.available_types = []
        
    def create_shap_analysis_window(self, model_name, model, X_test):
        """创建SHAP分析窗口"""
        self.shap_window = tk.Toplevel(self.parent_gui.root)
        self.shap_window.title(f"SHAP分析 - {model_name}")
        self.shap_window.geometry("1200x800")
        self.shap_window.minsize(1000, 600)
        
        # 生成SHAP数据
        self.model_name = model_name
        self.model = model
        self.X_test = X_test
        
        # 创建界面
        self._create_shap_interface()
        
        # 生成SHAP分析
        self._generate_shap_analysis()
        
    def _create_shap_interface(self):
        """创建SHAP界面"""
        # 控制面板
        control_frame = ttk.LabelFrame(self.shap_window, text="SHAP分析控制")
        control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 第一行：图表类型选择
        type_frame = ttk.Frame(control_frame)
        type_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(type_frame, text="图表类型:").pack(side=tk.LEFT, padx=(0, 5))
        self.shap_type_var = tk.StringVar(value="摘要图")
        self.shap_type_combo = ttk.Combobox(
            type_frame, 
            textvariable=self.shap_type_var,
            values=["摘要图", "依赖图", "力图", "决策图", "瀑布图"],
            state="readonly", 
            width=15
        )
        self.shap_type_combo.pack(side=tk.LEFT, padx=(0, 10))
        self.shap_type_combo.bind('<<ComboboxSelected>>', self._on_type_change)
        
        # 翻页控制（仅对多图类型显示）
        self.page_frame = ttk.Frame(type_frame)
        self.page_frame.pack(side=tk.LEFT, padx=(20, 0))
        
        self.prev_btn = ttk.Button(self.page_frame, text="◀ 上一页", command=self._prev_page, state="disabled")
        self.prev_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.page_label = ttk.Label(self.page_frame, text="1 / 1")
        self.page_label.pack(side=tk.LEFT, padx=(0, 5))
        
        self.next_btn = ttk.Button(self.page_frame, text="下一页 ▶", command=self._next_page, state="disabled")
        self.next_btn.pack(side=tk.LEFT)
        
        # 第二行：操作按钮
        btn_frame = ttk.Frame(control_frame)
        btn_frame.pack(fill=tk.X, padx=5, pady=(0, 5))
        
        ttk.Button(btn_frame, text="🔄 刷新", command=self._refresh_current).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_frame, text="💾 保存当前图", command=self._save_current).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_frame, text="📁 保存所有图", command=self._save_all).pack(side=tk.LEFT, padx=(0, 5))
        
        # 状态显示
        self.status_var = tk.StringVar(value="正在生成SHAP分析...")
        ttk.Label(btn_frame, textvariable=self.status_var, foreground="blue").pack(side=tk.RIGHT)
        
        # 图表显示区域
        chart_frame = ttk.LabelFrame(self.shap_window, text="SHAP图表显示")
        chart_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 创建matplotlib图表容器
        self.fig, self.ax = plt.subplots(figsize=(12, 8))
        self.canvas = FigureCanvasTkAgg(self.fig, chart_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 初始显示
        self.ax.text(0.5, 0.5, "正在生成SHAP分析，请稍候...", 
                    ha='center', va='center', transform=self.ax.transAxes, 
                    fontsize=16, bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue"))
        self.canvas.draw()
        
    def _generate_shap_analysis(self):
        """生成SHAP分析数据"""
        try:
            import shap
            
            self.status_var.set("正在计算SHAP值...")
            self.shap_window.update()
            
            # 生成SHAP值
            try:
                explainer = shap.TreeExplainer(self.model)
                shap_values = explainer.shap_values(self.X_test)
                explainer_type = "TreeExplainer"
            except Exception as e:
                self.parent_gui.log_message(f"TreeExplainer失败: {e}, 使用KernelExplainer")
                explainer = shap.KernelExplainer(self.model.predict_proba, self.X_test)
                shap_values = explainer.shap_values(self.X_test)
                explainer_type = "KernelExplainer"
            
            # 处理SHAP值格式
            if isinstance(shap_values, list) and len(shap_values) > 1:
                shap_values_for_analysis = shap_values[1]  # 正类
            elif isinstance(shap_values, np.ndarray) and shap_values.ndim == 3:
                shap_values_for_analysis = shap_values[:, :, 1]  # 正类
            else:
                shap_values_for_analysis = shap_values
            
            # 存储SHAP数据
            self.shap_data = {
                'explainer': explainer,
                'shap_values': shap_values,
                'shap_values_for_analysis': shap_values_for_analysis,
                'explainer_type': explainer_type
            }
            
            self.status_var.set(f"SHAP分析完成 ({explainer_type})")
            
            # 生成各类图表数据
            self._prepare_chart_data()
            
            # 显示第一个图表
            self._display_current_chart()
            
        except ImportError:
            self.status_var.set("SHAP库未安装")
            self.ax.clear()
            self.ax.text(0.5, 0.5, 'SHAP库未安装\n请安装: pip install shap',
                        ha='center', va='center', transform=self.ax.transAxes, 
                        fontsize=16, bbox=dict(boxstyle="round,pad=0.5", facecolor="lightyellow"))
            self.canvas.draw()
        except Exception as e:
            self.status_var.set(f"SHAP分析失败: {e}")
            self.ax.clear()
            self.ax.text(0.5, 0.5, f'SHAP分析失败:\n{str(e)}',
                        ha='center', va='center', transform=self.ax.transAxes, 
                        fontsize=14, bbox=dict(boxstyle="round,pad=0.5", facecolor="lightcoral"))
            self.canvas.draw()
    
    def _prepare_chart_data(self):
        """准备各类图表数据"""
        self.chart_data = {
            "摘要图": {"count": 1, "current": 0},
            "决策图": {"count": 1, "current": 0},
            "依赖图": {"count": 0, "current": 0, "features": []},
            "力图": {"count": 0, "current": 0},
            "瀑布图": {"count": 0, "current": 0, "indices": []}
        }
        
        # 计算依赖图数量（前5个重要特征）
        feature_importance = np.abs(self.shap_data['shap_values_for_analysis']).mean(0)
        top_features_idx = np.argsort(feature_importance)[::-1][:5]
        self.chart_data["依赖图"]["count"] = len(top_features_idx)
        self.chart_data["依赖图"]["features"] = top_features_idx
        
        # 计算力图数量（前3个样本）
        max_force_samples = min(3, len(self.X_test))
        self.chart_data["力图"]["count"] = max_force_samples
        
        # 计算瀑布图数量
        max_samples = len(self.X_test)
        waterfall_indices = [i for i in [0, 5, 10] if i < max_samples]
        if not waterfall_indices:
            waterfall_indices = [0]
        self.chart_data["瀑布图"]["count"] = len(waterfall_indices)
        self.chart_data["瀑布图"]["indices"] = waterfall_indices
        
        # 更新可用类型
        self.available_types = [t for t, data in self.chart_data.items() if data["count"] > 0]
        self.shap_type_combo['values'] = self.available_types
        
    def _on_type_change(self, event=None):
        """图表类型改变时的处理"""
        new_type = self.shap_type_var.get()
        if new_type != self.current_shap_type:
            self.current_shap_type = new_type
            self.current_page = 0
            self.chart_data[new_type]["current"] = 0
            self._update_page_controls()
            self._display_current_chart()
    
    def _update_page_controls(self):
        """更新翻页控制"""
        chart_info = self.chart_data[self.current_shap_type]
        total_pages = chart_info["count"]
        current_page = chart_info["current"] + 1
        
        # 更新页码显示
        self.page_label.config(text=f"{current_page} / {total_pages}")
        
        # 更新按钮状态
        self.prev_btn.config(state="normal" if chart_info["current"] > 0 else "disabled")
        self.next_btn.config(state="normal" if chart_info["current"] < total_pages - 1 else "disabled")
        
        # 显示/隐藏翻页控制
        if total_pages > 1:
            self.page_frame.pack(side=tk.LEFT, padx=(20, 0))
        else:
            self.page_frame.pack_forget()
    
    def _prev_page(self):
        """上一页"""
        chart_info = self.chart_data[self.current_shap_type]
        if chart_info["current"] > 0:
            chart_info["current"] -= 1
            self._update_page_controls()
            self._display_current_chart()
    
    def _next_page(self):
        """下一页"""
        chart_info = self.chart_data[self.current_shap_type]
        if chart_info["current"] < chart_info["count"] - 1:
            chart_info["current"] += 1
            self._update_page_controls()
            self._display_current_chart()
    
    def _display_current_chart(self):
        """显示当前图表"""
        try:
            self.ax.clear()
            chart_type = self.current_shap_type
            chart_info = self.chart_data[chart_type]
            current_idx = chart_info["current"]
            
            if chart_type == "摘要图":
                self._plot_summary()
            elif chart_type == "决策图":
                self._plot_decision()
            elif chart_type == "依赖图":
                feature_idx = chart_info["features"][current_idx]
                self._plot_dependence(feature_idx)
            elif chart_type == "力图":
                self._plot_force(current_idx)
            elif chart_type == "瀑布图":
                sample_idx = chart_info["indices"][current_idx]
                self._plot_waterfall(sample_idx)
            
            self.canvas.draw()
            
        except Exception as e:
            self.ax.clear()
            self.ax.text(0.5, 0.5, f'图表生成失败:\n{str(e)}',
                        ha='center', va='center', transform=self.ax.transAxes, 
                        fontsize=14, bbox=dict(boxstyle="round,pad=0.5", facecolor="lightcoral"))
            self.canvas.draw()

    def _plot_summary(self):
        """绘制SHAP摘要图"""
        import shap
        import tempfile
        import os
        from PIL import Image

        # 清除当前轴
        self.ax.clear()

        try:
            # 创建临时文件来保存SHAP图
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
                temp_path = tmp_file.name

            # 创建独立的图形来生成SHAP摘要图
            plt.figure(figsize=(10, 6))
            shap.summary_plot(
                self.shap_data['shap_values_for_analysis'],
                self.X_test,
                feature_names=self.X_test.columns.tolist() if hasattr(self.X_test, 'columns') else None,
                show=False,
                max_display=15
            )
            plt.title(f'{self.model_name} - SHAP摘要图', fontsize=14, pad=20)
            plt.tight_layout()
            plt.savefig(temp_path, dpi=150, bbox_inches='tight')
            plt.close()

            # 加载保存的图像并显示在主轴上
            img = Image.open(temp_path)
            self.ax.imshow(img)
            self.ax.axis('off')

            # 清理临时文件
            os.unlink(temp_path)

        except Exception as e:
            self.ax.text(0.5, 0.5, f'SHAP摘要图生成失败:\n{str(e)}',
                        ha='center', va='center', transform=self.ax.transAxes,
                        fontsize=12, bbox=dict(boxstyle="round,pad=0.5", facecolor="lightcoral"))



    def _plot_decision(self):
        """绘制SHAP决策图"""
        import shap
        import tempfile
        import os
        from PIL import Image

        # 清除当前轴
        self.ax.clear()

        # 处理数据格式
        if isinstance(self.X_test, np.ndarray):
            import pandas as pd
            feature_names = [f'feature_{i}' for i in range(self.X_test.shape[1])]
            X_test_df = pd.DataFrame(self.X_test, columns=feature_names)
            X_test_subset = X_test_df.iloc[:50]
        else:
            X_test_subset = self.X_test.iloc[:50]

        shap_values = self.shap_data['shap_values']
        explainer = self.shap_data['explainer']

        try:
            # 创建临时文件来保存SHAP图
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
                temp_path = tmp_file.name

            # 创建独立的图形来生成SHAP决策图
            plt.figure(figsize=(10, 6))
            if isinstance(shap_values, list) or (isinstance(shap_values, np.ndarray) and shap_values.ndim == 3):
                expected_val = explainer.expected_value[1] if isinstance(explainer.expected_value, (list, tuple, np.ndarray)) and len(explainer.expected_value) > 1 else explainer.expected_value
                shap_val = shap_values[..., 1][:50] if shap_values.ndim == 3 else shap_values[1][:50]
                shap.decision_plot(expected_val, shap_val, X_test_subset, show=False)
            else:
                shap.decision_plot(explainer.expected_value, shap_values[:50], X_test_subset, show=False)

            plt.title(f'{self.model_name} - SHAP决策图 (前50个样本)', fontsize=14, pad=20)
            plt.tight_layout()
            plt.savefig(temp_path, dpi=150, bbox_inches='tight')
            plt.close()

            # 加载保存的图像并显示在主轴上
            img = Image.open(temp_path)
            self.ax.imshow(img)
            self.ax.axis('off')

            # 清理临时文件
            os.unlink(temp_path)

        except Exception as e:
            self.ax.text(0.5, 0.5, f'SHAP决策图生成失败:\n{str(e)}',
                        ha='center', va='center', transform=self.ax.transAxes,
                        fontsize=12, bbox=dict(boxstyle="round,pad=0.5", facecolor="lightcoral"))

    def _plot_dependence(self, feature_idx):
        """绘制SHAP依赖图"""
        import shap
        import tempfile
        import os
        from PIL import Image

        # 清除当前轴
        self.ax.clear()

        try:
            # 获取特征名称
            if hasattr(self.X_test, 'columns'):
                feature_name = self.X_test.columns[feature_idx]
                feature_to_plot = feature_name
            else:
                feature_name = f'feature_{feature_idx}'
                feature_to_plot = feature_idx

            # 验证特征索引是否有效
            if isinstance(feature_to_plot, int) and feature_to_plot >= self.shap_data['shap_values_for_analysis'].shape[1]:
                raise ValueError(f"特征索引 {feature_to_plot} 超出范围")

            # 创建临时文件来保存SHAP图
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
                temp_path = tmp_file.name

            # 创建独立的图形来生成SHAP依赖图
            plt.figure(figsize=(10, 6))
            shap.dependence_plot(
                feature_to_plot,
                self.shap_data['shap_values_for_analysis'],
                self.X_test,
                show=False
            )
            plt.title(f'{self.model_name} - SHAP依赖图: {feature_name}', fontsize=14, pad=20)
            plt.tight_layout()
            plt.savefig(temp_path, dpi=150, bbox_inches='tight')
            plt.close()

            # 加载保存的图像并显示在主轴上
            img = Image.open(temp_path)
            self.ax.imshow(img)
            self.ax.axis('off')

            # 清理临时文件
            os.unlink(temp_path)

        except Exception as e:
            self.ax.text(0.5, 0.5, f'SHAP依赖图生成失败:\n{str(e)}\n特征索引: {feature_idx}',
                        ha='center', va='center', transform=self.ax.transAxes,
                        fontsize=12, bbox=dict(boxstyle="round,pad=0.5", facecolor="lightcoral"))

    def _plot_force(self, sample_idx):
        """绘制SHAP力图"""
        # 获取单个样本的SHAP值和特征值
        sample_shap_values = self.shap_data['shap_values_for_analysis'][sample_idx]
        if hasattr(self.X_test, 'iloc'):
            sample_features = self.X_test.iloc[sample_idx].values
        else:
            sample_features = self.X_test[sample_idx]

        # 获取特征名称
        if hasattr(self.X_test, 'columns'):
            feature_names = self.X_test.columns.tolist()
        else:
            feature_names = [f'feature_{i}' for i in range(self.X_test.shape[1])]

        # 获取基准值
        explainer = self.shap_data['explainer']
        if hasattr(explainer, 'expected_value'):
            if isinstance(explainer.expected_value, (list, tuple, np.ndarray)) and len(explainer.expected_value) > 1:
                base_value = explainer.expected_value[1]
            else:
                base_value = explainer.expected_value
        else:
            base_value = 0

        # 选择最重要的10个特征进行显示
        importance_order = np.argsort(np.abs(sample_shap_values))[::-1][:10]
        selected_shap = sample_shap_values[importance_order]
        selected_features = sample_features[importance_order]
        selected_names = [feature_names[idx] if idx < len(feature_names) else f'feature_{idx}' for idx in importance_order]

        # 创建条形图显示SHAP值
        colors = ['#FF4444' if v > 0 else '#4488FF' for v in selected_shap]
        bars = self.ax.barh(range(len(selected_shap)), selected_shap, color=colors, alpha=0.7)

        # 添加特征名和值标签
        labels = [f"{name} = {val:.3f}" for name, val in zip(selected_names, selected_features)]
        self.ax.set_yticks(range(len(selected_shap)))
        self.ax.set_yticklabels(labels)
        self.ax.set_xlabel('SHAP Value')
        self.ax.set_title(f'{self.model_name} - SHAP力图 (样本 {sample_idx+1})', fontsize=14, pad=20)
        self.ax.axvline(x=0, color='black', linestyle='-', alpha=0.3)
        self.ax.grid(True, alpha=0.3, axis='x')

        # 添加基准值和预测值信息
        prediction = base_value + np.sum(selected_shap)
        self.ax.text(0.02, 0.98, f'基准值: {base_value:.3f}\n预测值: {prediction:.3f}',
                    transform=self.ax.transAxes, verticalalignment='top',
                    bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    def _plot_waterfall(self, sample_idx):
        """绘制SHAP瀑布图"""
        import shap
        import tempfile
        import os
        from PIL import Image

        # 清除当前轴
        self.ax.clear()

        # 处理数据格式
        if isinstance(self.X_test, np.ndarray):
            sample_data = self.X_test[sample_idx]
            feature_names = [f'feature_{i}' for i in range(self.X_test.shape[1])]
        else:
            sample_data = self.X_test.iloc[sample_idx].values
            feature_names = list(self.X_test.columns)

        shap_values = self.shap_data['shap_values']
        explainer = self.shap_data['explainer']

        try:
            # 创建临时文件来保存SHAP图
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
                temp_path = tmp_file.name

            # 创建独立的图形来生成SHAP瀑布图
            plt.figure(figsize=(10, 6))
            if isinstance(shap_values, list) or (isinstance(shap_values, np.ndarray) and shap_values.ndim == 3):
                expected_val = explainer.expected_value[1] if isinstance(explainer.expected_value, (list, tuple, np.ndarray)) and len(explainer.expected_value) > 1 else explainer.expected_value
                shap_val = shap_values[..., 1][sample_idx] if shap_values.ndim == 3 else shap_values[1][sample_idx]
                exp = shap.Explanation(values=shap_val,
                                       base_values=expected_val,
                                       data=sample_data,
                                       feature_names=feature_names)
            else:
                exp = shap.Explanation(values=shap_values[sample_idx],
                                       base_values=explainer.expected_value,
                                       data=sample_data,
                                       feature_names=feature_names)

            shap.plots.waterfall(exp, max_display=15, show=False)
            plt.title(f'{self.model_name} - SHAP瀑布图 (样本 {sample_idx})', fontsize=14, pad=20)
            plt.tight_layout()
            plt.savefig(temp_path, dpi=150, bbox_inches='tight')
            plt.close()

            # 加载保存的图像并显示在主轴上
            img = Image.open(temp_path)
            self.ax.imshow(img)
            self.ax.axis('off')

            # 清理临时文件
            os.unlink(temp_path)

        except Exception as e:
            self.ax.text(0.5, 0.5, f'SHAP瀑布图生成失败:\n{str(e)}',
                        ha='center', va='center', transform=self.ax.transAxes,
                        fontsize=12, bbox=dict(boxstyle="round,pad=0.5", facecolor="lightcoral"))

    def _refresh_current(self):
        """刷新当前图表"""
        self._display_current_chart()

    def _save_current(self):
        """保存当前图表 - 支持PNG和PDF格式"""
        from tkinter import filedialog

        chart_type = self.current_shap_type
        chart_info = self.chart_data[chart_type]
        current_idx = chart_info["current"]

        # 创建保存格式选择对话框
        save_dialog = tk.Toplevel(self.shap_window)
        save_dialog.title("保存SHAP图表")
        save_dialog.geometry("350x200")
        save_dialog.resizable(False, False)
        save_dialog.transient(self.shap_window)
        save_dialog.grab_set()

        # 居中显示
        save_dialog.update_idletasks()
        x = (save_dialog.winfo_screenwidth() // 2) - (save_dialog.winfo_width() // 2)
        y = (save_dialog.winfo_screenheight() // 2) - (save_dialog.winfo_height() // 2)
        save_dialog.geometry(f"+{x}+{y}")

        # 标题
        ttk.Label(save_dialog, text="选择保存格式:", font=("Arial", 12, "bold")).pack(pady=15)

        # 格式选择
        format_var = tk.StringVar(value="png")
        format_frame = ttk.Frame(save_dialog)
        format_frame.pack(pady=10)

        ttk.Radiobutton(format_frame, text="PNG格式 (推荐)",
                       variable=format_var, value="png").pack(anchor=tk.W, padx=20, pady=5)
        ttk.Radiobutton(format_frame, text="PDF格式",
                       variable=format_var, value="pdf").pack(anchor=tk.W, padx=20, pady=5)

        # 按钮框架
        button_frame = ttk.Frame(save_dialog)
        button_frame.pack(pady=20)

        def execute_save():
            file_format = format_var.get()
            save_dialog.destroy()
            self._save_current_with_format(chart_type, current_idx, file_format)

        ttk.Button(button_frame, text="保存", command=execute_save).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=save_dialog.destroy).pack(side=tk.LEFT, padx=5)

    def _save_current_with_format(self, chart_type, current_idx, file_format):
        """使用指定格式保存当前图表"""
        from tkinter import filedialog

        # 生成默认文件名
        if chart_type in ["依赖图", "力图", "瀑布图"]:
            if file_format == "pdf":
                default_name = f"{self.model_name}_shap_{chart_type}_{current_idx+1}.pdf"
            else:
                default_name = f"{self.model_name}_shap_{chart_type}_{current_idx+1}.png"
        else:
            if file_format == "pdf":
                default_name = f"{self.model_name}_shap_{chart_type}.pdf"
            else:
                default_name = f"{self.model_name}_shap_{chart_type}.png"

        # 设置文件类型
        if file_format == "pdf":
            filetypes = [("PDF files", "*.pdf"), ("All files", "*.*")]
            defaultextension = ".pdf"
        else:
            filetypes = [("PNG files", "*.png"), ("All files", "*.*")]
            defaultextension = ".png"

        filename = filedialog.asksaveasfilename(
            title="保存SHAP图表",
            defaultextension=defaultextension,
            initialfile=default_name,
            filetypes=filetypes
        )

        if filename:
            try:
                # 根据图表类型重新生成并保存正确的图表
                if chart_type == "摘要图":
                    self._save_summary_chart_with_format(filename, file_format)
                elif chart_type == "决策图":
                    self._save_decision_chart_with_format(filename, file_format)
                elif chart_type == "依赖图":
                    feature_idx = self.chart_data["依赖图"]["features"][current_idx]
                    self._save_dependence_chart_with_format(filename, feature_idx, file_format)
                elif chart_type == "力图":
                    self._save_force_chart_with_format(filename, current_idx, file_format)
                elif chart_type == "瀑布图":
                    sample_idx = self.chart_data["瀑布图"]["indices"][current_idx]
                    self._save_waterfall_chart_with_format(filename, sample_idx, file_format)
                else:
                    # 如果是未知类型，使用原来的方法
                    if file_format == "pdf":
                        # 保留原始图层的PDF保存参数
                        self.fig.savefig(filename,
                                       format='pdf',
                                       dpi=300,
                                       bbox_inches='tight',
                                       facecolor='white',
                                       edgecolor='none',
                                       transparent=False,
                                       pad_inches=0.1,
                                       metadata={'Creator': 'Multi-Model Analysis Tool',
                                               'Subject': f'SHAP Analysis - {chart_type}',
                                               'Title': f'{self.model_name} SHAP {chart_type}',
                                               'Keywords': 'SHAP, Machine Learning, Interpretability'})
                    else:
                        # PNG格式保存
                        self.fig.savefig(filename, dpi=300, bbox_inches='tight')

                format_name = "PDF" if file_format == "pdf" else "PNG"
                messagebox.showinfo("成功", f"{format_name}格式的SHAP图表已保存至:\n{filename}")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败:\n{e}")

    def _save_all(self):
        """保存所有SHAP图表 - 支持PNG和PDF格式选择"""
        from tkinter import filedialog
        import tempfile
        import os

        # 创建保存选项对话框
        save_dialog = tk.Toplevel(self.shap_window)
        save_dialog.title("批量保存SHAP图表")
        save_dialog.geometry("400x300")
        save_dialog.resizable(False, False)
        save_dialog.transient(self.shap_window)
        save_dialog.grab_set()

        # 居中显示
        save_dialog.update_idletasks()
        x = (save_dialog.winfo_screenwidth() // 2) - (save_dialog.winfo_width() // 2)
        y = (save_dialog.winfo_screenheight() // 2) - (save_dialog.winfo_height() // 2)
        save_dialog.geometry(f"+{x}+{y}")

        # 标题
        ttk.Label(save_dialog, text="批量保存选项", font=("Arial", 14, "bold")).pack(pady=15)

        # 保存方式选择
        ttk.Label(save_dialog, text="选择保存方式:", font=("Arial", 10)).pack(pady=(10, 5))
        save_option = tk.StringVar(value="separate")

        ttk.Radiobutton(save_dialog, text="分别保存为独立文件",
                       variable=save_option, value="separate").pack(anchor=tk.W, padx=30, pady=2)
        ttk.Radiobutton(save_dialog, text="保存到单个PDF文件",
                       variable=save_option, value="single_pdf").pack(anchor=tk.W, padx=30, pady=2)

        # 格式选择（仅对分别保存有效）
        ttk.Label(save_dialog, text="文件格式:", font=("Arial", 10)).pack(pady=(15, 5))
        format_var = tk.StringVar(value="png")
        format_frame = ttk.Frame(save_dialog)
        format_frame.pack(pady=5)

        format_png = ttk.Radiobutton(format_frame, text="PNG", variable=format_var, value="png")
        format_png.pack(side=tk.LEFT, padx=10)
        format_pdf = ttk.Radiobutton(format_frame, text="PDF", variable=format_var, value="pdf")
        format_pdf.pack(side=tk.LEFT, padx=10)

        # 动态启用/禁用格式选择
        def on_save_option_change():
            if save_option.get() == "single_pdf":
                format_png.config(state="disabled")
                format_pdf.config(state="disabled")
            else:
                format_png.config(state="normal")
                format_pdf.config(state="normal")

        save_option.trace('w', lambda *args: on_save_option_change())

        # 按钮框架
        button_frame = ttk.Frame(save_dialog)
        button_frame.pack(pady=20)

        def execute_save():
            option = save_option.get()
            file_format = format_var.get()
            save_dialog.destroy()

            if option == "single_pdf":
                self._save_all_to_single_pdf()
            else:
                self._save_all_separate_files(file_format)

        ttk.Button(button_frame, text="保存", command=execute_save).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=save_dialog.destroy).pack(side=tk.LEFT, padx=5)

    def _save_all_separate_files(self, file_format):
        """分别保存所有SHAP图表为独立文件"""
        from tkinter import filedialog

        folder = filedialog.askdirectory(title="选择保存目录")
        if not folder:
            return

        folder_path = Path(folder)
        saved_count = 0

        try:
            # 保存所有类型的图表
            for chart_type in self.available_types:
                chart_info = self.chart_data[chart_type]

                for i in range(chart_info["count"]):
                    try:
                        # 生成文件名
                        if chart_type == "摘要图":
                            filename = f"{self.model_name}_shap_summary.{file_format}"
                            save_path = folder_path / filename
                            self._save_summary_chart_with_format(save_path, file_format)
                        elif chart_type == "决策图":
                            filename = f"{self.model_name}_shap_decision.{file_format}"
                            save_path = folder_path / filename
                            self._save_decision_chart_with_format(save_path, file_format)
                        elif chart_type == "依赖图":
                            feature_idx = chart_info["features"][i]
                            feature_name = self.X_test.columns[feature_idx] if hasattr(self.X_test, 'columns') else f'feature_{feature_idx}'
                            filename = f"{self.model_name}_shap_dependence_{feature_name}.{file_format}"
                            save_path = folder_path / filename
                            self._save_dependence_chart_with_format(save_path, feature_idx, file_format)
                        elif chart_type == "力图":
                            filename = f"{self.model_name}_shap_force_sample_{i+1}.{file_format}"
                            save_path = folder_path / filename
                            self._save_force_chart_with_format(save_path, i, file_format)
                        elif chart_type == "瀑布图":
                            sample_idx = chart_info["indices"][i]
                            filename = f"{self.model_name}_shap_waterfall_{sample_idx}.{file_format}"
                            save_path = folder_path / filename
                            self._save_waterfall_chart_with_format(save_path, sample_idx, file_format)

                        saved_count += 1

                    except Exception as e:
                        print(f"保存 {chart_type} 第 {i+1} 张图失败: {e}")
                        continue

            format_name = "PNG" if file_format == "png" else "PDF"
            messagebox.showinfo("成功", f"已保存 {saved_count} 个{format_name}格式的SHAP图表至:\n{folder_path}")

        except Exception as e:
            messagebox.showerror("错误", f"批量保存失败:\n{e}")

    def _save_all_to_single_pdf(self):
        """保存所有SHAP图表到单个PDF文件"""
        from tkinter import filedialog
        from matplotlib.backends.backend_pdf import PdfPages
        from datetime import datetime

        # 选择保存路径
        file_path = filedialog.asksaveasfilename(
            title="保存SHAP分析报告",
            defaultextension=".pdf",
            initialfile=f"{self.model_name}_SHAP_Analysis_Report.pdf",
            filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")]
        )

        if not file_path:
            return

        try:
            with PdfPages(file_path) as pdf:
                # 创建封面页
                self._create_shap_pdf_cover(pdf)

                # 保存所有类型的图表
                total_charts = sum(self.chart_data[chart_type]["count"] for chart_type in self.available_types)
                current_chart = 0

                for chart_type in self.available_types:
                    chart_info = self.chart_data[chart_type]

                    for i in range(chart_info["count"]):
                        current_chart += 1
                        self.status_var.set(f"正在生成PDF... ({current_chart}/{total_charts})")
                        self.shap_window.update()

                        try:
                            # 创建图表
                            if chart_type == "摘要图":
                                self._add_summary_to_pdf(pdf)
                            elif chart_type == "决策图":
                                self._add_decision_to_pdf(pdf)
                            elif chart_type == "依赖图":
                                feature_idx = chart_info["features"][i]
                                self._add_dependence_to_pdf(pdf, feature_idx)
                            elif chart_type == "力图":
                                self._add_force_to_pdf(pdf, i)
                            elif chart_type == "瀑布图":
                                sample_idx = chart_info["indices"][i]
                                self._add_waterfall_to_pdf(pdf, sample_idx)

                        except Exception as e:
                            print(f"添加 {chart_type} 第 {i+1} 张图到PDF失败: {e}")
                            continue

            self.status_var.set("SHAP分析完成")
            messagebox.showinfo("成功", f"SHAP分析报告已保存至:\n{file_path}")

        except Exception as e:
            self.status_var.set("PDF生成失败")
            messagebox.showerror("错误", f"生成PDF报告失败:\n{e}")

    def _save_summary_chart(self, save_path):
        """保存SHAP摘要图"""
        import shap

        plt.figure(figsize=(10, 6))
        shap.summary_plot(
            self.shap_data['shap_values_for_analysis'],
            self.X_test,
            feature_names=self.X_test.columns.tolist() if hasattr(self.X_test, 'columns') else None,
            show=False,
            max_display=15
        )
        plt.title(f'{self.model_name} - SHAP Summary Plot', fontsize=14, pad=20)
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

    def _save_decision_chart(self, save_path):
        """保存SHAP决策图"""
        import shap

        # 处理数据格式
        if isinstance(self.X_test, np.ndarray):
            import pandas as pd
            feature_names = [f'feature_{i}' for i in range(self.X_test.shape[1])]
            X_test_df = pd.DataFrame(self.X_test, columns=feature_names)
            X_test_subset = X_test_df.iloc[:50]
        else:
            X_test_subset = self.X_test.iloc[:50]

        shap_values = self.shap_data['shap_values']
        explainer = self.shap_data['explainer']

        plt.figure(figsize=(10, 6))
        if isinstance(shap_values, list) or (isinstance(shap_values, np.ndarray) and shap_values.ndim == 3):
            expected_val = explainer.expected_value[1] if isinstance(explainer.expected_value, (list, tuple, np.ndarray)) and len(explainer.expected_value) > 1 else explainer.expected_value
            shap_val = shap_values[..., 1][:50] if shap_values.ndim == 3 else shap_values[1][:50]
            shap.decision_plot(expected_val, shap_val, X_test_subset, show=False)
        else:
            shap.decision_plot(explainer.expected_value, shap_values[:50], X_test_subset, show=False)

        plt.title(f'{self.model_name} - SHAP Decision Plot (Top 50 Samples)', fontsize=14, pad=20)
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

    def _save_dependence_chart(self, save_path, feature_idx):
        """保存SHAP依赖图"""
        import shap

        # 获取特征名称
        if hasattr(self.X_test, 'columns'):
            feature_name = self.X_test.columns[feature_idx]
            feature_to_plot = feature_name
        else:
            feature_name = f'feature_{feature_idx}'
            feature_to_plot = feature_idx

        plt.figure(figsize=(10, 6))
        shap.dependence_plot(
            feature_to_plot,
            self.shap_data['shap_values_for_analysis'],
            self.X_test,
            show=False
        )
        plt.title(f'{self.model_name} - SHAP Dependence Plot: {feature_name}', fontsize=14, pad=20)
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

    def _save_force_chart(self, save_path, sample_idx):
        """保存SHAP力图"""
        # 获取单个样本的SHAP值和特征值
        sample_shap_values = self.shap_data['shap_values_for_analysis'][sample_idx]
        if hasattr(self.X_test, 'iloc'):
            sample_features = self.X_test.iloc[sample_idx].values
        else:
            sample_features = self.X_test[sample_idx]

        # 获取特征名称
        if hasattr(self.X_test, 'columns'):
            feature_names = self.X_test.columns.tolist()
        else:
            feature_names = [f'feature_{i}' for i in range(self.X_test.shape[1])]

        # 获取基准值
        explainer = self.shap_data['explainer']
        if hasattr(explainer, 'expected_value'):
            if isinstance(explainer.expected_value, (list, tuple, np.ndarray)) and len(explainer.expected_value) > 1:
                base_value = explainer.expected_value[1]
            else:
                base_value = explainer.expected_value
        else:
            base_value = 0

        # 选择最重要的10个特征进行显示
        importance_order = np.argsort(np.abs(sample_shap_values))[::-1][:10]
        selected_shap = sample_shap_values[importance_order]
        selected_features = sample_features[importance_order]
        selected_names = [feature_names[idx] if idx < len(feature_names) else f'feature_{idx}' for idx in importance_order]

        # 创建条形图显示SHAP值
        plt.figure(figsize=(10, 6))
        colors = ['#FF4444' if v > 0 else '#4488FF' for v in selected_shap]
        bars = plt.barh(range(len(selected_shap)), selected_shap, color=colors, alpha=0.7)

        # 添加特征名和值标签
        labels = [f"{name} = {val:.3f}" for name, val in zip(selected_names, selected_features)]
        plt.yticks(range(len(selected_shap)), labels)
        plt.xlabel('SHAP Value')
        plt.title(f'{self.model_name} - SHAP Force Plot (Sample {sample_idx+1})', fontsize=14, pad=20)
        plt.axvline(x=0, color='black', linestyle='-', alpha=0.3)
        plt.grid(True, alpha=0.3, axis='x')

        # 添加基准值和预测值信息
        prediction = base_value + np.sum(selected_shap)
        plt.text(0.02, 0.98, f'Base Value: {base_value:.3f}\nPrediction: {prediction:.3f}',
                transform=plt.gca().transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

    def _save_waterfall_chart(self, save_path, sample_idx):
        """保存SHAP瀑布图"""
        import shap

        # 处理数据格式
        if isinstance(self.X_test, np.ndarray):
            sample_data = self.X_test[sample_idx]
            feature_names = [f'feature_{i}' for i in range(self.X_test.shape[1])]
        else:
            sample_data = self.X_test.iloc[sample_idx].values
            feature_names = list(self.X_test.columns)

        shap_values = self.shap_data['shap_values']
        explainer = self.shap_data['explainer']

        plt.figure(figsize=(10, 6))
        if isinstance(shap_values, list) or (isinstance(shap_values, np.ndarray) and shap_values.ndim == 3):
            expected_val = explainer.expected_value[1] if isinstance(explainer.expected_value, (list, tuple, np.ndarray)) and len(explainer.expected_value) > 1 else explainer.expected_value
            shap_val = shap_values[..., 1][sample_idx] if shap_values.ndim == 3 else shap_values[1][sample_idx]
            exp = shap.Explanation(values=shap_val,
                                   base_values=expected_val,
                                   data=sample_data,
                                   feature_names=feature_names)
        else:
            exp = shap.Explanation(values=shap_values[sample_idx],
                                   base_values=explainer.expected_value,
                                   data=sample_data,
                                   feature_names=feature_names)

        shap.plots.waterfall(exp, max_display=15, show=False)
        plt.title(f'{self.model_name} - SHAP Waterfall Plot (Sample {sample_idx})', fontsize=14, pad=20)
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

    # 支持格式选择的保存方法
    def _save_summary_chart_with_format(self, save_path, file_format):
        """保存SHAP摘要图（支持格式选择）"""
        import shap

        plt.figure(figsize=(10, 6))
        shap.summary_plot(
            self.shap_data['shap_values_for_analysis'],
            self.X_test,
            feature_names=self.X_test.columns.tolist() if hasattr(self.X_test, 'columns') else None,
            show=False,
            max_display=15
        )
        plt.title(f'{self.model_name} - SHAP Summary Plot', fontsize=14, pad=20)
        plt.tight_layout()

        if file_format == "pdf":
            # 保留原始图层的PDF保存参数
            plt.savefig(save_path,
                       format='pdf',
                       dpi=300,
                       bbox_inches='tight',
                       facecolor='white',
                       edgecolor='none',
                       transparent=False,
                       pad_inches=0.1,
                       metadata={'Creator': 'Multi-Model Analysis Tool',
                               'Subject': 'SHAP Summary Plot',
                               'Title': f'{self.model_name} SHAP Summary',
                               'Keywords': 'SHAP, Summary, Machine Learning, Interpretability'})
        else:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

    def _save_decision_chart_with_format(self, save_path, file_format):
        """保存SHAP决策图（支持格式选择）"""
        import shap

        # 处理数据格式
        if isinstance(self.X_test, np.ndarray):
            import pandas as pd
            feature_names = [f'feature_{i}' for i in range(self.X_test.shape[1])]
            X_test_df = pd.DataFrame(self.X_test, columns=feature_names)
            X_test_subset = X_test_df.iloc[:50]
        else:
            X_test_subset = self.X_test.iloc[:50]

        shap_values = self.shap_data['shap_values']
        explainer = self.shap_data['explainer']

        plt.figure(figsize=(10, 6))
        if isinstance(shap_values, list) or (isinstance(shap_values, np.ndarray) and shap_values.ndim == 3):
            expected_val = explainer.expected_value[1] if isinstance(explainer.expected_value, (list, tuple, np.ndarray)) and len(explainer.expected_value) > 1 else explainer.expected_value
            shap_val = shap_values[..., 1][:50] if shap_values.ndim == 3 else shap_values[1][:50]
            shap.decision_plot(expected_val, shap_val, X_test_subset, show=False)
        else:
            shap.decision_plot(explainer.expected_value, shap_values[:50], X_test_subset, show=False)

        plt.title(f'{self.model_name} - SHAP Decision Plot (Top 50 Samples)', fontsize=14, pad=20)
        plt.tight_layout()

        if file_format == "pdf":
            # 保留原始图层的PDF保存参数
            plt.savefig(save_path,
                       format='pdf',
                       dpi=300,
                       bbox_inches='tight',
                       facecolor='white',
                       edgecolor='none',
                       transparent=False,
                       pad_inches=0.1,
                       metadata={'Creator': 'Multi-Model Analysis Tool',
                               'Subject': 'SHAP Decision Plot',
                               'Title': f'{self.model_name} SHAP Decision',
                               'Keywords': 'SHAP, Decision, Machine Learning, Interpretability'})
        else:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

    def _save_dependence_chart_with_format(self, save_path, feature_idx, file_format):
        """保存SHAP依赖图（支持格式选择）"""
        import shap

        try:
            # 获取特征名称
            if hasattr(self.X_test, 'columns'):
                feature_name = self.X_test.columns[feature_idx]
                feature_to_plot = feature_name
            else:
                feature_name = f'feature_{feature_idx}'
                feature_to_plot = feature_idx

            # 验证特征索引是否有效
            if isinstance(feature_to_plot, int) and feature_to_plot >= self.shap_data['shap_values_for_analysis'].shape[1]:
                raise ValueError(f"特征索引 {feature_to_plot} 超出范围")

            plt.figure(figsize=(10, 6))
            shap.dependence_plot(
                feature_to_plot,
                self.shap_data['shap_values_for_analysis'],
                self.X_test,
                show=False
            )
            plt.title(f'{self.model_name} - SHAP Dependence Plot: {feature_name}', fontsize=14, pad=20)
            plt.tight_layout()

        except Exception as e:
            # 如果SHAP依赖图生成失败，创建一个错误提示图
            plt.figure(figsize=(10, 6))
            plt.text(0.5, 0.5, f'SHAP依赖图生成失败:\n{str(e)}\n特征: {feature_name}',
                    ha='center', va='center', fontsize=12,
                    bbox=dict(boxstyle="round,pad=0.5", facecolor="lightcoral"))
            plt.xlim(0, 1)
            plt.ylim(0, 1)
            plt.axis('off')
            plt.title(f'{self.model_name} - SHAP Dependence Plot: {feature_name} (错误)', fontsize=14, pad=20)

        if file_format == "pdf":
            # 保留原始图层的PDF保存参数
            plt.savefig(save_path,
                       format='pdf',
                       dpi=300,
                       bbox_inches='tight',
                       facecolor='white',
                       edgecolor='none',
                       transparent=False,
                       pad_inches=0.1,
                       metadata={'Creator': 'Multi-Model Analysis Tool',
                               'Subject': 'SHAP Dependence Plot',
                               'Title': f'{self.model_name} SHAP Dependence',
                               'Keywords': 'SHAP, Dependence, Machine Learning, Interpretability'})
        else:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

    def _save_force_chart_with_format(self, save_path, sample_idx, file_format):
        """保存SHAP力图（支持格式选择）"""
        # 获取单个样本的SHAP值和特征值
        sample_shap_values = self.shap_data['shap_values_for_analysis'][sample_idx]
        if hasattr(self.X_test, 'iloc'):
            sample_features = self.X_test.iloc[sample_idx].values
        else:
            sample_features = self.X_test[sample_idx]

        # 获取特征名称
        if hasattr(self.X_test, 'columns'):
            feature_names = self.X_test.columns.tolist()
        else:
            feature_names = [f'feature_{i}' for i in range(self.X_test.shape[1])]

        # 获取基准值
        explainer = self.shap_data['explainer']
        if hasattr(explainer, 'expected_value'):
            if isinstance(explainer.expected_value, (list, tuple, np.ndarray)) and len(explainer.expected_value) > 1:
                base_value = explainer.expected_value[1]
            else:
                base_value = explainer.expected_value
        else:
            base_value = 0

        # 选择最重要的10个特征进行显示
        importance_order = np.argsort(np.abs(sample_shap_values))[::-1][:10]
        selected_shap = sample_shap_values[importance_order]
        selected_features = sample_features[importance_order]
        selected_names = [feature_names[idx] if idx < len(feature_names) else f'feature_{idx}' for idx in importance_order]

        # 创建条形图显示SHAP值
        plt.figure(figsize=(10, 6))
        colors = ['#FF4444' if v > 0 else '#4488FF' for v in selected_shap]
        bars = plt.barh(range(len(selected_shap)), selected_shap, color=colors, alpha=0.7)

        # 添加特征名和值标签
        labels = [f"{name} = {val:.3f}" for name, val in zip(selected_names, selected_features)]
        plt.yticks(range(len(selected_shap)), labels)
        plt.xlabel('SHAP Value')
        plt.title(f'{self.model_name} - SHAP Force Plot (Sample {sample_idx+1})', fontsize=14, pad=20)
        plt.axvline(x=0, color='black', linestyle='-', alpha=0.3)
        plt.grid(True, alpha=0.3, axis='x')

        # 添加基准值和预测值信息
        prediction = base_value + np.sum(selected_shap)
        plt.text(0.02, 0.98, f'Base Value: {base_value:.3f}\nPrediction: {prediction:.3f}',
                transform=plt.gca().transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

        plt.tight_layout()

        if file_format == "pdf":
            # 保留原始图层的PDF保存参数
            plt.savefig(save_path,
                       format='pdf',
                       dpi=300,
                       bbox_inches='tight',
                       facecolor='white',
                       edgecolor='none',
                       transparent=False,
                       pad_inches=0.1,
                       metadata={'Creator': 'Multi-Model Analysis Tool',
                               'Subject': 'SHAP Force Plot',
                               'Title': f'{self.model_name} SHAP Force',
                               'Keywords': 'SHAP, Force, Machine Learning, Interpretability'})
        else:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

    def _save_waterfall_chart_with_format(self, save_path, sample_idx, file_format):
        """保存SHAP瀑布图（支持格式选择）"""
        import shap

        # 处理数据格式
        if isinstance(self.X_test, np.ndarray):
            sample_data = self.X_test[sample_idx]
            feature_names = [f'feature_{i}' for i in range(self.X_test.shape[1])]
        else:
            sample_data = self.X_test.iloc[sample_idx].values
            feature_names = list(self.X_test.columns)

        shap_values = self.shap_data['shap_values']
        explainer = self.shap_data['explainer']

        plt.figure(figsize=(10, 6))
        if isinstance(shap_values, list) or (isinstance(shap_values, np.ndarray) and shap_values.ndim == 3):
            expected_val = explainer.expected_value[1] if isinstance(explainer.expected_value, (list, tuple, np.ndarray)) and len(explainer.expected_value) > 1 else explainer.expected_value
            shap_val = shap_values[..., 1][sample_idx] if shap_values.ndim == 3 else shap_values[1][sample_idx]
            exp = shap.Explanation(values=shap_val,
                                   base_values=expected_val,
                                   data=sample_data,
                                   feature_names=feature_names)
        else:
            exp = shap.Explanation(values=shap_values[sample_idx],
                                   base_values=explainer.expected_value,
                                   data=sample_data,
                                   feature_names=feature_names)

        shap.plots.waterfall(exp, max_display=15, show=False)
        plt.title(f'{self.model_name} - SHAP Waterfall Plot (Sample {sample_idx})', fontsize=14, pad=20)
        plt.tight_layout()

        if file_format == "pdf":
            # 保留原始图层的PDF保存参数
            plt.savefig(save_path,
                       format='pdf',
                       dpi=300,
                       bbox_inches='tight',
                       facecolor='white',
                       edgecolor='none',
                       transparent=False,
                       pad_inches=0.1,
                       metadata={'Creator': 'Multi-Model Analysis Tool',
                               'Subject': 'SHAP Waterfall Plot',
                               'Title': f'{self.model_name} SHAP Waterfall',
                               'Keywords': 'SHAP, Waterfall, Machine Learning, Interpretability'})
        else:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

    # PDF相关的辅助方法
    def _create_shap_pdf_cover(self, pdf):
        """创建SHAP分析报告封面"""
        from datetime import datetime

        fig, ax = plt.subplots(figsize=(8.5, 11))
        ax.axis('off')

        # 标题
        ax.text(0.5, 0.8, 'SHAP Analysis Report', ha='center', va='center',
                fontsize=24, fontweight='bold', transform=ax.transAxes)

        # 模型信息
        ax.text(0.5, 0.65, f'Model: {self.model_name}', ha='center', va='center',
                fontsize=16, transform=ax.transAxes)

        # 数据信息
        ax.text(0.5, 0.55, f'Test Samples: {self.X_test.shape[0]}', ha='center', va='center',
                fontsize=14, transform=ax.transAxes)
        ax.text(0.5, 0.5, f'Features: {self.X_test.shape[1]}', ha='center', va='center',
                fontsize=14, transform=ax.transAxes)

        # 生成时间
        ax.text(0.5, 0.3, f'Generated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}',
                ha='center', va='center', fontsize=12, transform=ax.transAxes)

        # 图表统计
        total_charts = sum(self.chart_data[chart_type]["count"] for chart_type in self.available_types)
        ax.text(0.5, 0.2, f'Total Charts: {total_charts}', ha='center', va='center',
                fontsize=12, transform=ax.transAxes)

        pdf.savefig(fig, bbox_inches='tight')
        plt.close(fig)

    def _add_summary_to_pdf(self, pdf):
        """添加摘要图到PDF"""
        import shap

        fig = plt.figure(figsize=(10, 6))
        shap.summary_plot(
            self.shap_data['shap_values_for_analysis'],
            self.X_test,
            feature_names=self.X_test.columns.tolist() if hasattr(self.X_test, 'columns') else None,
            show=False,
            max_display=15
        )
        plt.title(f'{self.model_name} - SHAP Summary Plot', fontsize=14, pad=20)
        plt.tight_layout()
        pdf.savefig(fig, bbox_inches='tight')
        plt.close(fig)

    def _add_decision_to_pdf(self, pdf):
        """添加决策图到PDF"""
        import shap

        # 处理数据格式
        if isinstance(self.X_test, np.ndarray):
            import pandas as pd
            feature_names = [f'feature_{i}' for i in range(self.X_test.shape[1])]
            X_test_df = pd.DataFrame(self.X_test, columns=feature_names)
            X_test_subset = X_test_df.iloc[:50]
        else:
            X_test_subset = self.X_test.iloc[:50]

        shap_values = self.shap_data['shap_values']
        explainer = self.shap_data['explainer']

        fig = plt.figure(figsize=(10, 6))
        if isinstance(shap_values, list) or (isinstance(shap_values, np.ndarray) and shap_values.ndim == 3):
            expected_val = explainer.expected_value[1] if isinstance(explainer.expected_value, (list, tuple, np.ndarray)) and len(explainer.expected_value) > 1 else explainer.expected_value
            shap_val = shap_values[..., 1][:50] if shap_values.ndim == 3 else shap_values[1][:50]
            shap.decision_plot(expected_val, shap_val, X_test_subset, show=False)
        else:
            shap.decision_plot(explainer.expected_value, shap_values[:50], X_test_subset, show=False)

        plt.title(f'{self.model_name} - SHAP Decision Plot (Top 50 Samples)', fontsize=14, pad=20)
        plt.tight_layout()
        pdf.savefig(fig, bbox_inches='tight')
        plt.close(fig)

    def _add_dependence_to_pdf(self, pdf, feature_idx):
        """添加依赖图到PDF"""
        import shap

        try:
            # 获取特征名称
            if hasattr(self.X_test, 'columns'):
                feature_name = self.X_test.columns[feature_idx]
                feature_to_plot = feature_name
            else:
                feature_name = f'feature_{feature_idx}'
                feature_to_plot = feature_idx

            # 验证特征索引是否有效
            if isinstance(feature_to_plot, int) and feature_to_plot >= self.shap_data['shap_values_for_analysis'].shape[1]:
                raise ValueError(f"特征索引 {feature_to_plot} 超出范围")

            fig = plt.figure(figsize=(10, 6))
            shap.dependence_plot(
                feature_to_plot,
                self.shap_data['shap_values_for_analysis'],
                self.X_test,
                show=False
            )
            plt.title(f'{self.model_name} - SHAP Dependence Plot: {feature_name}', fontsize=14, pad=20)
            plt.tight_layout()

        except Exception as e:
            # 如果SHAP依赖图生成失败，创建一个错误提示图
            fig = plt.figure(figsize=(10, 6))
            plt.text(0.5, 0.5, f'SHAP依赖图生成失败:\n{str(e)}\n特征: {feature_name}',
                    ha='center', va='center', fontsize=12,
                    bbox=dict(boxstyle="round,pad=0.5", facecolor="lightcoral"))
            plt.xlim(0, 1)
            plt.ylim(0, 1)
            plt.axis('off')
            plt.title(f'{self.model_name} - SHAP Dependence Plot: {feature_name} (错误)', fontsize=14, pad=20)

        # 保存到PDF - 保留原始图层的参数
        pdf.savefig(fig,
                   bbox_inches='tight',
                   facecolor='white',
                   edgecolor='none',
                   transparent=False,
                   pad_inches=0.1)
        plt.close(fig)

    def _add_force_to_pdf(self, pdf, sample_idx):
        """添加力图到PDF"""
        # 获取单个样本的SHAP值和特征值
        sample_shap_values = self.shap_data['shap_values_for_analysis'][sample_idx]
        if hasattr(self.X_test, 'iloc'):
            sample_features = self.X_test.iloc[sample_idx].values
        else:
            sample_features = self.X_test[sample_idx]

        # 获取特征名称
        if hasattr(self.X_test, 'columns'):
            feature_names = self.X_test.columns.tolist()
        else:
            feature_names = [f'feature_{i}' for i in range(self.X_test.shape[1])]

        # 获取基准值
        explainer = self.shap_data['explainer']
        if hasattr(explainer, 'expected_value'):
            if isinstance(explainer.expected_value, (list, tuple, np.ndarray)) and len(explainer.expected_value) > 1:
                base_value = explainer.expected_value[1]
            else:
                base_value = explainer.expected_value
        else:
            base_value = 0

        # 选择最重要的10个特征进行显示
        importance_order = np.argsort(np.abs(sample_shap_values))[::-1][:10]
        selected_shap = sample_shap_values[importance_order]
        selected_features = sample_features[importance_order]
        selected_names = [feature_names[idx] if idx < len(feature_names) else f'feature_{idx}' for idx in importance_order]

        # 创建条形图显示SHAP值
        fig = plt.figure(figsize=(10, 6))
        colors = ['#FF4444' if v > 0 else '#4488FF' for v in selected_shap]
        bars = plt.barh(range(len(selected_shap)), selected_shap, color=colors, alpha=0.7)

        # 添加特征名和值标签
        labels = [f"{name} = {val:.3f}" for name, val in zip(selected_names, selected_features)]
        plt.yticks(range(len(selected_shap)), labels)
        plt.xlabel('SHAP Value')
        plt.title(f'{self.model_name} - SHAP Force Plot (Sample {sample_idx+1})', fontsize=14, pad=20)
        plt.axvline(x=0, color='black', linestyle='-', alpha=0.3)
        plt.grid(True, alpha=0.3, axis='x')

        # 添加基准值和预测值信息
        prediction = base_value + np.sum(selected_shap)
        plt.text(0.02, 0.98, f'Base Value: {base_value:.3f}\nPrediction: {prediction:.3f}',
                transform=plt.gca().transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

        plt.tight_layout()
        pdf.savefig(fig, bbox_inches='tight')
        plt.close(fig)

    def _add_waterfall_to_pdf(self, pdf, sample_idx):
        """添加瀑布图到PDF"""
        import shap

        # 处理数据格式
        if isinstance(self.X_test, np.ndarray):
            sample_data = self.X_test[sample_idx]
            feature_names = [f'feature_{i}' for i in range(self.X_test.shape[1])]
        else:
            sample_data = self.X_test.iloc[sample_idx].values
            feature_names = list(self.X_test.columns)

        shap_values = self.shap_data['shap_values']
        explainer = self.shap_data['explainer']

        fig = plt.figure(figsize=(10, 6))
        if isinstance(shap_values, list) or (isinstance(shap_values, np.ndarray) and shap_values.ndim == 3):
            expected_val = explainer.expected_value[1] if isinstance(explainer.expected_value, (list, tuple, np.ndarray)) and len(explainer.expected_value) > 1 else explainer.expected_value
            shap_val = shap_values[..., 1][sample_idx] if shap_values.ndim == 3 else shap_values[1][sample_idx]
            exp = shap.Explanation(values=shap_val,
                                   base_values=expected_val,
                                   data=sample_data,
                                   feature_names=feature_names)
        else:
            exp = shap.Explanation(values=shap_values[sample_idx],
                                   base_values=explainer.expected_value,
                                   data=sample_data,
                                   feature_names=feature_names)

        shap.plots.waterfall(exp, max_display=15, show=False)
        plt.title(f'{self.model_name} - SHAP Waterfall Plot (Sample {sample_idx})', fontsize=14, pad=20)
        plt.tight_layout()
        pdf.savefig(fig, bbox_inches='tight')
        plt.close(fig)